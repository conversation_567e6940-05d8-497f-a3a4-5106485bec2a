<?php

use App\Enums\OrderCancellationReasonEnum;

return [

    /*
    |--------------------------------------------------------------------------
    | Communications Language Lines
    |--------------------------------------------------------------------------
    |
    | The following language lines are used during communications for various
    | messages that we need to send to the user. You are free to modify
    | these language lines according to your application's requirements.
    |
    */

    'order' => [
        'new-prescription' => [
            'order-signed' => 'Your order from Dr. :providerUserLastName has been received by :distributorName. They will try to reach you in the next 48 business hours. You can also contact them at :distributorPhone if you have any questions. You are now enrolled to receive medical device order notifications via text. Message frequency varies. Reply STOP to opt-out. Msg&Data rates may apply. Terms and Privacy Policy apply',
            'pending-initial-order-review' => ':distributorName: Hi :patientName, we\'ve begun processing your order and will keep you updated every step of the way. If you have any questions, please call us at :distributorPhone. Msg freq varies. Reply STOP to opt-out. Msg&Data rates may apply. Terms & Privacy apply.',
            'pending-benefits-investigation' => ':distributorName: We are now reviewing your insurance benefits and will keep you posted with updates. Msg freq varies. Reply STOP to opt-out. Msg&Data rates may apply. Terms & Privacy apply.',
            'pending-customer-feedback' => ':distributorName: Hi :patientName, we need to speak with you to proceed. To expedite the process, please call us at :distributorPhone. We\'ll also be reaching out to you shortly. Msg freq varies. Reply STOP to opt-out. Msg&Data rates may apply. Terms and Privacy Policy apply.',
            'pending-document-collection' => ':distributorName: We are actively working to gather the necessary documents to process your order. Msg freq varies. Reply STOP to opt-out. Msg&Data rates may apply. Terms & Privacy apply.',
            'pending-pre-authorization' => ':distributorName: We are now working with your insurance to obtain authorization, which may take up to 14 business days. Msg freq varies. Reply STOP to opt-out. Msg&Data rates may apply. Terms and Privacy Policy apply.',
            'pending-shipping-confirmation' => ':distributorName: Hi :patientName, we need your confirmation to proceed with your order. To expedite the process, please call us at :distributorPhone. Msg freq varies. Reply STOP to opt-out. Msg&Data rates may apply. Terms and Privacy Policy apply.',
            'shipped_with_tracking' => ':distributorName: Your order has been shipped with Tracking number :trackingNumber. If you have questions, please call us at :distributorPhone. Msg freq varies. Reply STOP to opt-out. Msg&Data rates may apply. Terms and Privacy Policy apply',
            'shipped_without_tracking' => ':distributorName: Your order has been shipped! If you have any questions, please call us at :distributorPhone. Msg freq varies. Reply STOP to opt-out. Msg&Data rates may apply. Terms and Privacy Policy apply.',
            'cancelled' => [
                OrderCancellationReasonEnum::FAILED_TO_CONNECT_WITH_CUSTOMER->name => ':distributorName: Hi :patientName, we were unable to reach you regarding your order with us, so it has been canceled. If you\'d like to proceed, please call us at :distributorPhone and we\'ll be happy to assist! Msg freq varies. Reply STOP to opt-out. Msg&Data rates may apply. Terms and Privacy Policy apply.',
                OrderCancellationReasonEnum::DOES_NOT_WANT_TO_PROCEED_WITH_ORDER->name => ':distributorName: Hi :patientName, per your request, your order with us has been cancelled. Msg freq varies. Reply STOP to opt-out. Msg&Data rates may apply. Terms and Privacy Policy apply.',
                OrderCancellationReasonEnum::PATIENT_OUT_OF_POCKET_TOO_HIGH->name => ':distributorName: Hi :patientName, per your request, your order with us has been cancelled. Msg freq varies. Reply STOP to opt-out. Msg&Data rates may apply. Terms and Privacy Policy apply.',
                OrderCancellationReasonEnum::OUT_OF_NETWORK_WITH_THE_PAYER->name => [
                    'rerouted' => ':distributorName: Hi :patientName, since we are out of network with your insurance provider, your order has been transferred to :assignedDistributorName. If you have questions, call us at :distributorPhone. Msg freq varies. Reply STOP to opt-out. Msg&Data rates may apply. Terms and Privacy Policy apply.',
                    'not_rerouted' => ':distributorName: Hi :patientName, we are unable to process your order at this time. If you have questions, please call us at :distributorPhone. Msg freq varies. Reply STOP to opt-out. Msg&Data rates may apply. Terms and Privacy Policy apply.',
                ],
                OrderCancellationReasonEnum::ON_THERAPY_WITH_ANOTHER_SUPPLIER->name => ':distributorName: Your order has been cancelled. If you have any questions, please call us at :distributorPhone. Msg freq varies. Reply STOP to opt-out. Msg&Data rates may apply. Terms and Privacy Policy apply.',
                OrderCancellationReasonEnum::PRE_AUTHORIZATION_NOT_APPROVED->name => ':distributorName: Hi :patientName, your insurance has denied the prior authorization request. If you have any questions, call us at :distributorPhone. Msg freq varies. Reply STOP to opt-out. Msg&Data rates may apply. Terms and Privacy Policy apply.',
                OrderCancellationReasonEnum::DOES_NOT_MEET_MEDICAL_NECESSITY->name => ':distributorName: Hi :patientName, we are unable to process your order at this time. If you have any questions, please call us at :distributorPhone. Msg freq varies. Reply STOP to opt-out. Msg&Data rates may apply. Terms and Privacy Policy apply.',
            ],
        ],
        'prescription-renewal' => [
            'pending-provider-onboarding' => [
                'title' => 'Complete your account set up with Shuttle Health',
                'body' => 'To whom it may concern, We here at :distributorName have simplified our process for completing Prescription Renewals. Onboarding onto this new system takes less than 5 minutes, has no costs to you and you will immediately begin to reap the benefits and time savings. To Get Started...',
            ],
            'pending-physician-onboarding' => [
                'title' => 'Verify and set up your Shuttle Health Account',
                'body' => 'Your facility admin has set up your Shuttle Health account for :providerName. Shuttle health is designed to simplify physician and office staff life when they prescribe medical devices and responding to renewal prescription document request. To start using Shuttle Health, simply follow this invitation link: <a href=\":invitationLink\">Set Up My Account</a>',
            ],
            'pending-clinical-documentation-review' => '',
            'pending-patient-evaluation' => 'This is :providerName, We are reaching out because you are due for an visit, please contact us at :providerPhone to schedule an appointment.',
        ],
        'scheduled' => [
            'pending-pre-authorization' => ':distributorName here! It is taking longer than usual to get the authorization, we are working with your insurance provider to get this completed. We will send you an update you once its done. You are now enrolled to receive medical device order notifications via text. Message frequency varies. Reply STOP to opt-out. Msg&Data rates may apply. Terms and Privacy Policy apply.',
        ],
    ],
    'patient' => [
        'start' => 'Welcome to :organizationName! You are now enrolled to receive medical device order notifications via text. Message frequency varies. Reply STOP to opt-out. Msg&Data rates may apply. Terms and Privacy Policy apply.',
        'stop' => 'You have been unenrolled from :organizationName text messages. You will not receive any more messages from :organizationName. Reply START to resubscribe.',
        'help' => 'If needing help for your order, please contact :distributorName at :distributorPhone. If this is a medical emergency, please call  911.',
    ],
];
