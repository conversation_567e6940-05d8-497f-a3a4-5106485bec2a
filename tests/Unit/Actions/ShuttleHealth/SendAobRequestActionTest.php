<?php

namespace Tests\Unit\Actions\ShuttleHealth;

use App\Actions\Dme\SendAobRequestAction;
use App\Enums\DistributorAobTemplateNameEnum;
use App\Enums\DocumentRequestTypeEnum;
use App\Enums\OrderTypeEnum;
use App\Models\DocumentRequest;
use App\Models\PatientAobRequest;
use App\Notifications\Mail\AobRequestEmail;
use App\Notifications\Sms\AobDocumentRequestNotification;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Notification;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class SendAobRequestActionTest extends TestCase
{
    use RefreshDatabase;

    private SendAobRequestAction $action;

    public function setUp(): void
    {
        parent::setUp();

        $this->initializeValidOrder(OrderTypeEnum::NEW_PRESCRIPTION, linkToDistributor: true);

        $this->action = app(SendAobRequestAction::class);

        Notification::fake();
    }

    #[Test]
    public function it_creates_patient_aob_request_record()
    {
        $documentRequest = DocumentRequest::factory()
            ->for($this->order->patient->globalPatient)
            ->create(['type' => DocumentRequestTypeEnum::AOB]);

        $aobRequestData = [
            'aob_template_name' => DistributorAobTemplateNameEnum::AOB_APPY_EN->value,
            'delivered_by_email' => true,
            'delivered_by_phone' => false,
        ];

        $this->action->execute(
            $aobRequestData,
            $this->order->patient,
            $documentRequest,
            $this->distributor->owner,
            $this->distributor,
            $this->order
        );

        $this->assertDatabaseHas(PatientAobRequest::class, [
            'patient_id' => $this->order->patient->id,
            'document_request_id' => $documentRequest->id,
            'aob_template_name' => DistributorAobTemplateNameEnum::AOB_APPY_EN,
        ]);
    }

    #[Test]
    public function it_sends_email_notification_when_delivered_by_email_is_true()
    {
        $documentRequest = DocumentRequest::factory()
            ->for($this->order->patient->globalPatient)
            ->create(['type' => DocumentRequestTypeEnum::AOB]);

        $aobRequestData = [
            'aob_template_name' => DistributorAobTemplateNameEnum::AOB_APPY_EN->value,
            'delivered_by_email' => true,
            'delivered_by_phone' => false,
        ];

        $this->action->execute(
            $aobRequestData,
            $this->order->patient,
            $documentRequest,
            $this->distributor->owner,
            $this->distributor,
            $this->order
        );

        Notification::assertSentTo(
            $this->order->patient,
            AobRequestEmail::class
        );
    }

    #[Test]
    public function it_sends_sms_notification_when_delivered_by_phone_is_true()
    {
        $documentRequest = DocumentRequest::factory()
            ->for($this->order->patient->globalPatient)
            ->create(['type' => DocumentRequestTypeEnum::AOB]);

        $aobRequestData = [
            'aob_template_name' => DistributorAobTemplateNameEnum::AOB_APPY_EN->value,
            'delivered_by_email' => false,
            'delivered_by_phone' => true,
        ];

        $this->action->execute(
            $aobRequestData,
            $this->order->patient,
            $documentRequest,
            $this->distributor->owner,
            $this->distributor,
            $this->order
        );

        Notification::assertSentTo(
            $this->order->patient,
            AobDocumentRequestNotification::class
        );
    }

    #[Test]
    public function it_sends_both_notifications_when_both_delivery_methods_are_true()
    {
        $documentRequest = DocumentRequest::factory()
            ->for($this->order->patient->globalPatient)
            ->create(['type' => DocumentRequestTypeEnum::AOB]);

        $aobRequestData = [
            'aob_template_name' => DistributorAobTemplateNameEnum::AOB_APPY_EN->value,
            'delivered_by_email' => true,
            'delivered_by_phone' => true,
        ];

        $this->action->execute(
            $aobRequestData,
            $this->order->patient,
            $documentRequest,
            $this->distributor->owner,
            $this->distributor,
            $this->order
        );

        Notification::assertSentTo(
            $this->order->patient,
            AobRequestEmail::class
        );

        Notification::assertSentTo(
            $this->order->patient,
            AobDocumentRequestNotification::class
        );
    }

    #[Test]
    public function it_does_not_send_notifications_when_delivery_methods_are_false()
    {
        $documentRequest = DocumentRequest::factory()
            ->for($this->order->patient->globalPatient)
            ->create(['type' => DocumentRequestTypeEnum::AOB]);

        $aobRequestData = [
            'aob_template_name' => DistributorAobTemplateNameEnum::AOB_APPY_EN->value,
            'delivered_by_email' => false,
            'delivered_by_phone' => false,
        ];

        $this->action->execute(
            $aobRequestData,
            $this->order->patient,
            $documentRequest,
            $this->distributor->owner,
            $this->distributor,
            $this->order
        );

        Notification::assertNothingSent();
    }

    #[Test]
    public function it_generates_unique_token_for_each_request()
    {
        $documentRequest1 = DocumentRequest::factory()
            ->for($this->order->patient->globalPatient)
            ->create(['type' => DocumentRequestTypeEnum::AOB]);

        $documentRequest2 = DocumentRequest::factory()
            ->for($this->order->patient->globalPatient)
            ->create(['type' => DocumentRequestTypeEnum::AOB]);

        $aobRequestData = [
            'aob_template_name' => DistributorAobTemplateNameEnum::AOB_APPY_EN->value,
            'delivered_by_email' => true,
            'delivered_by_phone' => false,
        ];

        $this->action->execute(
            $aobRequestData,
            $this->order->patient,
            $documentRequest1,
            $this->distributor->owner,
            $this->distributor,
            $this->order
        );

        $this->action->execute(
            $aobRequestData,
            $this->order->patient,
            $documentRequest2,
            $this->distributor->owner,
            $this->distributor,
            $this->order
        );

        $tokens = PatientAobRequest::pluck('token')->toArray();
        $this->assertCount(2, array_unique($tokens));
    }
}
