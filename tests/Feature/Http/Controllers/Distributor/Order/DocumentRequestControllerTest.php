<?php

namespace Tests\Feature\Http\Controllers\Distributor\Order;

use App\Enums\DistributorAobTemplateNameEnum;
use App\Enums\DocumentRequestHistoryTypeEnum;
use App\Enums\DocumentRequestTypeEnum;
use App\Enums\OrderTypeEnum;
use App\Enums\OrganizationSettingNameEnum;
use App\Enums\ValueTypeEnum;
use App\Models\DocumentRequest;
use App\Models\Facility;
use App\Notifications\Mail\AobRequestEmail;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Notification;
use Illuminate\Support\Facades\Storage;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class DocumentRequestControllerTest extends TestCase
{
    use RefreshDatabase;
    use WithFaker;

    public function setUp(): void
    {
        parent::setUp();

        $this->initializeValidOrder(OrderTypeEnum::NEW_PRESCRIPTION);
        $this->order->distributor()->associate($this->distributor)->save();

        $this->token = $this->fakeCognitoToken(['int-dme-scope'], $this->distributor->owner->cognito_id ?? null);
    }

    #[Test]
    public function it_can_create_order_document_request_cmn_with_custom_details()
    {
        $documentationDisk = config('filesystems.documentation');
        Storage::fake($documentationDisk);

        $cmnDetails = [
            'cmn' => [
                'branded' => true,
                'product_ids' => [$this->order->products()->first()->id],
            ],
        ];

        $response = $this->postJson(
            route('distributors.orders.document-requests.store', [$this->distributor, $this->order]),
            [
                'fax_number' => '**********',
                'is_digital' => false,
                'order_document_requests' => [
                    [
                        'type' => DocumentRequestTypeEnum::CMN->value,
                        'details' => $cmnDetails,
                    ],
                    [
                        'type' => DocumentRequestTypeEnum::LAB->value,
                        'details' => [
                            'labs' => [
                                ['name' => 'lab one'],
                                ['name' => 'lab two'],
                            ],
                        ],
                    ],
                    [
                        'type' => DocumentRequestTypeEnum::CUSTOM->value,
                        'details' => ['message' => 'custom message'],
                    ],
                ],
            ],
        );
        $response->assertSuccessful();

        $this->assertDatabaseCount('document_requests', 3);
        $this->assertDatabaseCount('files', 3);
    }

    #[Test]
    public function it_can_create_order_document_request_cmn_for_order_without_provider()
    {
        $documentationDisk = config('filesystems.documentation');
        Storage::fake($documentationDisk);

        $facility = Facility::factory()
            ->for($this->distributor)
            ->for($this->order->providerUser)
            ->create();

        $this->order->provider()->dissociate()->save();
        $this->order->facility()->associate($facility)->save();

        $cmnDetails = [
            'cmn' => [
                'branded' => true,
                'product_ids' => [$this->order->products()->first()->id],
            ],
        ];

        $response = $this->postJson(
            route('distributors.orders.document-requests.store', [$this->distributor, $this->order]),
            [
                'fax_number' => '**********',
                'is_digital' => false,
                'order_document_requests' => [
                    [
                        'type' => DocumentRequestTypeEnum::CMN->value,
                        'details' => $cmnDetails,
                    ],
                    [
                        'type' => DocumentRequestTypeEnum::LAB->value,
                        'details' => [
                            'labs' => [
                                ['name' => 'lab one'],
                                ['name' => 'lab two'],
                            ],
                        ],
                    ],
                    [
                        'type' => DocumentRequestTypeEnum::CUSTOM->value,
                        'details' => ['message' => 'custom message'],
                    ],
                ],
            ],
        );
        $response->assertSuccessful();

        $this->assertDatabaseCount('document_requests', 3);
        $this->assertDatabaseCount('files', 3);
    }

    #[Test]
    public function it_can_create_second_order_document_request_cmn_with_custom_details()
    {
        $documentationDisk = config('filesystems.documentation');
        Storage::fake($documentationDisk);

        DocumentRequest::factory()
            ->for($this->order)
            ->for($this->distributor)
            ->for($this->order->globalPatient)
            ->create(['type' => DocumentRequestTypeEnum::CMN]);

        $productIds = $this->order->products()->pluck('id');

        $response = $this->postJson(
            route(
                'distributors.orders.document-requests.store',
                [$this->distributor, $this->order],
            ),
            [
                'fax_number' => '**********',
                'is_digital' => false,
                'order_document_requests' => [
                    [
                        'type' => DocumentRequestTypeEnum::CMN->value,
                        'label' => 'branded cmn',
                        'details' => [
                            'cmn' => [
                                'branded' => true,
                                'product_ids' => [$productIds->first()],
                            ],
                        ],
                    ],
                    [
                        'type' => DocumentRequestTypeEnum::CMN->value,
                        'label' => 'generic cmn',
                        'details' => [
                            'cmn' => [
                                'branded' => false,
                                'product_ids' => $productIds->toArray(),
                            ],
                        ],
                    ],
                    [
                        'type' => DocumentRequestTypeEnum::LAB->value,
                        'details' => [
                            'labs' => [
                                ['name' => 'lab one'],
                                ['name' => 'lab two'],
                            ],
                        ],
                    ],
                    [
                        'type' => DocumentRequestTypeEnum::CUSTOM->value,
                        'details' => ['message' => 'custom message'],
                    ],
                ],
            ],
        );
        $response->assertSuccessful();

        $this->assertDatabaseCount('document_requests', 5);
        $this->assertDatabaseCount('files', 4);

        $this->assertDatabaseHas('document_requests', [
            'order_id' => $this->order->id,
            'distributor_id' => $this->distributor->id,
            'global_patient_id' => $this->order->global_patient_id,
            'label' => 'branded cmn',
            'type' => DocumentRequestTypeEnum::CMN->value,
            'details->cmn->branded' => true,
        ]);

        $this->assertDatabaseHas('document_requests', [
            'order_id' => $this->order->id,
            'distributor_id' => $this->distributor->id,
            'global_patient_id' => $this->order->global_patient_id,
            'label' => 'generic cmn',
            'type' => DocumentRequestTypeEnum::CMN->value,
            'details->cmn->branded' => false,
        ]);

        $this->assertDatabaseHas('document_requests', [
            'order_id' => $this->order->id,
            'distributor_id' => $this->distributor->id,
            'global_patient_id' => $this->order->global_patient_id,
            'type' => DocumentRequestTypeEnum::LAB->value,
        ]);

        $this->assertDatabaseHas('document_requests', [
            'order_id' => $this->order->id,
            'distributor_id' => $this->distributor->id,
            'global_patient_id' => $this->order->global_patient_id,
            'type' => DocumentRequestTypeEnum::CUSTOM->value,
            'details->message' => 'custom message',
        ]);
    }

    #[Test]
    public function it_can_create_order_document_requests()
    {
        $documentationDisk = config('filesystems.documentation');
        Storage::fake($documentationDisk);

        $response = $this->postJson(
            route(
                'distributors.orders.document-requests.store',
                [$this->distributor, $this->order],
            ),
            [
                'fax_number' => '**********',
                'is_digital' => false,
                'order_document_requests' => [
                    [
                        'type' => DocumentRequestTypeEnum::CMN->value,
                    ],
                    [
                        'type' => DocumentRequestTypeEnum::LAB->value,
                        'details' => [
                            'labs' => [
                                ['name' => 'lab one'],
                                ['name' => 'lab two'],
                            ],
                        ],
                    ],
                    [
                        'type' => DocumentRequestTypeEnum::CUSTOM->value,
                        'details' => ['message' => 'custom message'],
                    ],
                ],
            ],
        );
        $response->assertSuccessful();

        $this->assertDatabaseCount('document_requests', 3);
        $this->assertDatabaseCount('document_request_history', 3);
        $this->assertDatabaseCount('files', 3);

        $documentRequests = DocumentRequest::query()->where('order_id', $this->order->id)->get();

        $this->assertDatabaseHas('document_request_history', [
            'document_request_id' => $documentRequests->where('type', DocumentRequestTypeEnum::CMN)->first()->id,
            'type' => DocumentRequestHistoryTypeEnum::REQUESTED_ANALOG->value,
            'details' => null,
        ]);

        $this->assertDatabaseHas('document_request_history', [
            'document_request_id' => $documentRequests->where('type', DocumentRequestTypeEnum::LAB)->first()->id,
            'type' => DocumentRequestHistoryTypeEnum::REQUESTED_ANALOG->value,
            'details->labs->0->name' => 'lab one',
            'details->labs->1->name' => 'lab two',
        ]);

        $this->assertDatabaseHas('document_request_history', [
            'document_request_id' => $documentRequests->where('type', DocumentRequestTypeEnum::CUSTOM)->first()->id,
            'type' => DocumentRequestHistoryTypeEnum::REQUESTED_ANALOG->value,
            'details->message' => 'custom message',
        ]);
    }

    #[Test]
    public function it_can_create_digital_document_requests()
    {
        $documentationDisk = config('filesystems.documentation');
        Storage::fake($documentationDisk);

        // NOTE: provider user is required for digital requests and must be associated with the provider

        $response = $this->postJson(
            route(
                'distributors.orders.document-requests.store',
                [$this->distributor, $this->order],
            ),
            [
                'is_digital' => true,
                'order_document_requests' => [
                    [
                        'type' => DocumentRequestTypeEnum::CMN->value,
                    ],
                    [
                        'type' => DocumentRequestTypeEnum::LAB->value,
                        'details' => [
                            'labs' => [
                                ['name' => 'lab one'],
                                ['name' => 'lab two'],
                            ],
                        ],
                    ],
                    [
                        'type' => DocumentRequestTypeEnum::CUSTOM->value,
                        'details' => ['message' => 'custom message'],
                    ],
                ],
            ],
        );
        $response->assertSuccessful();

        $this->assertDatabaseCount('document_requests', 3);
        $this->assertDatabaseCount('document_request_history', 3);
        $this->assertDatabaseCount('files', 0);

        $documentRequests = DocumentRequest::query()->where('order_id', $this->order->id)->get();

        $this->assertDatabaseHas('document_request_history', [
            'document_request_id' => $documentRequests->where('type', DocumentRequestTypeEnum::CMN)->first()->id,
            'type' => DocumentRequestHistoryTypeEnum::REQUESTED_DIGITAL->value,
            'details' => null,
        ]);

        $this->assertDatabaseHas('document_request_history', [
            'document_request_id' => $documentRequests->where('type', DocumentRequestTypeEnum::LAB)->first()->id,
            'type' => DocumentRequestHistoryTypeEnum::REQUESTED_DIGITAL->value,
            'details->labs->0->name' => 'lab one',
            'details->labs->1->name' => 'lab two',
        ]);

        $this->assertDatabaseHas('document_request_history', [
            'document_request_id' => $documentRequests->where('type', DocumentRequestTypeEnum::CUSTOM)->first()->id,
            'type' => DocumentRequestHistoryTypeEnum::REQUESTED_DIGITAL->value,
            'details->message' => 'custom message',
        ]);
    }

    #[Test]
    public function it_can_create_order_document_request_aob()
    {
        Notification::fake();

        $this->distributor->organizationSettings()->create([
            'name' => OrganizationSettingNameEnum::AOB_TEMPLATE_NAMES,
            'value_type' => ValueTypeEnum::ARRAY,
            'value' => [DistributorAobTemplateNameEnum::AOB_APPY_EN],
        ]);

        $response = $this->postJson(
            route(
                'distributors.orders.document-requests.aob.request',
                [$this->distributor, $this->order],
            ),
            [
                'aob_template_name' => DistributorAobTemplateNameEnum::AOB_APPY_EN,
                'delivered_by_email' => true,
                'delivered_by_phone' => false,
            ],
        );

        $response->assertSuccessful();
        $this->assertDatabaseCount('document_requests', 1);
    }

    #[Test]
    public function it_receive_exception_on_create_order_document_request_aob_with_wrong_template_name()
    {
        $this->distributor->organizationSettings()->create([
            'name' => OrganizationSettingNameEnum::AOB_TEMPLATE_NAMES,
            'value_type' => ValueTypeEnum::ARRAY,
            'value' => [DistributorAobTemplateNameEnum::AOB_APPY_EN],
        ]);

        $this->postJson(
            route(
                'distributors.orders.document-requests.aob.request',
                [$this->distributor, $this->order],
            ),
            [
                'aob_template_name' => 'wrongTemplateName',
                'delivered_by_email' => true,
                'delivered_by_phone' => false,
            ],
        )->assertUnprocessable();
    }

    #[Test]
    public function it_requires_patient_email_for_aob_email_delivery()
    {
        $this->setOrganizationContext($this->distributor);

        $this->order->patient->update(['email' => null]);

        $this->distributor->organizationSettings()->create([
            'name' => OrganizationSettingNameEnum::AOB_TEMPLATE_NAMES,
            'value_type' => ValueTypeEnum::ARRAY,
            'value' => [DistributorAobTemplateNameEnum::AOB_APPY_EN],
        ]);

        $this->postJson(
            route(
                'distributors.orders.document-requests.aob.request',
                [$this->distributor, $this->order],
            ),
            [
                'aob_template_name' => DistributorAobTemplateNameEnum::AOB_APPY_EN,
                'delivered_by_email' => true,
                'delivered_by_phone' => false,
            ],
        )->assertStatus(422);
    }

    #[Test]
    public function it_requires_patient_phone_for_aob_phone_delivery()
    {
        $this->setOrganizationContext($this->distributor);

        $this->order->patient->update([
            'mobile' => null,
            'home_phone' => null,
            'work_phone' => null,
        ]);

        $this->distributor->organizationSettings()->create([
            'name' => OrganizationSettingNameEnum::AOB_TEMPLATE_NAMES,
            'value_type' => ValueTypeEnum::ARRAY,
            'value' => [DistributorAobTemplateNameEnum::AOB_APPY_EN],
        ]);

        $this->postJson(
            route(
                'distributors.orders.document-requests.aob.request',
                [$this->distributor, $this->order],
            ),
            [
                'aob_template_name' => DistributorAobTemplateNameEnum::AOB_APPY_EN,
                'delivered_by_email' => false,
                'delivered_by_phone' => true,
            ],
        )->assertStatus(422);
    }

    #[Test]
    public function it_can_create_aob_request_with_phone_delivery()
    {
        $this->distributor->organizationSettings()->create([
            'name' => OrganizationSettingNameEnum::AOB_TEMPLATE_NAMES,
            'value_type' => ValueTypeEnum::ARRAY,
            'value' => [DistributorAobTemplateNameEnum::AOB_APPY_EN],
        ]);

        $response = $this->postJson(
            route(
                'distributors.orders.document-requests.aob.request',
                [$this->distributor, $this->order],
            ),
            [
                'aob_template_name' => DistributorAobTemplateNameEnum::AOB_APPY_EN,
                'delivered_by_email' => false,
                'delivered_by_phone' => true,
            ],
        );

        $response->assertSuccessful();
        $this->assertDatabaseCount('document_requests', 1);
    }
}
