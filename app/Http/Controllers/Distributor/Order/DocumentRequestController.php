<?php

namespace App\Http\Controllers\Distributor\Order;

use App\Actions\Dme\SendAobRequestAction;
use App\Actions\ShuttleHealth\CreateDocumentRequest;
use App\Actions\ShuttleHealth\MakeCmnPDF;
use App\Actions\ShuttleHealth\PrepareAobFormData;
use App\Actions\ShuttleHealth\SendDigitalRequest;
use App\Actions\ShuttleHealth\SendRequestDocumentationFax;
use App\Enums\DistributorAobTemplateNameEnum;
use App\Enums\DocumentRequestRequestTypeEnum;
use App\Enums\DocumentRequestTypeEnum;
use App\Http\Controllers\Controller;
use App\Http\Requests\Distributor\Order\CreateAobDocumentRequest;
use App\Http\Requests\Distributor\Order\RequestDocumentationRequest;
use App\Http\Resources\DocumentRequestResource;
use App\Models\Distributor;
use App\Models\Order;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;

/**
 * Class DocumentRequestController
 *
 * Controller for managing document requests in orders.
 */
class DocumentRequestController extends Controller
{
    public function store(
        RequestDocumentationRequest $request,
        Distributor $distributor,
        Order $order,
        CreateDocumentRequest $createDocumentRequestAction,
        SendRequestDocumentationFax $sendRequestDocumentationFax,
        SendDigitalRequest $sendDigitalRequestAction,
    ): Response {
        $this->authorize('read-order', [$distributor, $order]);
        $this->authorize('write-document-request', $order);

        abort_if(
            empty($distributor->fax),
            Response::HTTP_UNPROCESSABLE_ENTITY,
            __('errors.distributor.has_no_fax_number'),
        );

        abort_if(
            empty($order->providerUser),
            Response::HTTP_UNPROCESSABLE_ENTITY,
            __('errors.order.has_no_provider_user'),
        );

        $isDigital = $request->validated('is_digital');

        abort_if(
            $isDigital && !$order->provider,
            Response::HTTP_UNPROCESSABLE_ENTITY,
            __('errors.order.has_no_provider'),
        );

        if ($isDigital) {
            DB::transaction(function () use ($request, $order, $distributor, $createDocumentRequestAction, $sendDigitalRequestAction) {
                foreach ($request->validated('order_document_requests') as $documentRequestData) {
                    $requestData = [
                        'order_id' => $order->id,
                        'patient_id' => $order->patient->id,
                        'provider_id' => $order->provider_id,
                        'facility_id' => $order->facility_id,
                        'is_digital' => true,
                        ...$documentRequestData,
                    ];

                    $documentRequest = $createDocumentRequestAction->execute(
                        distributor: $distributor,
                        user: getUser(),
                        requestType: DocumentRequestRequestTypeEnum::NEW_PRESCRIPTION,
                        documentRequestData: $requestData,
                        providerUser: $order->providerUser,
                    );

                    $sendDigitalRequestAction->execute(
                        documentRequest: $documentRequest,
                        user: getUser(),
                        details: $documentRequestData['details'] ?? null,
                    );
                }
            });
        } else {
            $sendRequestDocumentationFax->execute(
                distributor: $distributor,
                patient: $order->patient,
                providerUser: $order->providerUser,
                user: getUser(),
                faxNumber: $request->validated('fax_number'),
                documentRequestsData: $request->validated('order_document_requests'),
                provider: $order->provider,
                facility: $order->facility,
                order: $order,
            );
        }

        return response()->noContent();
    }

    public function cmnPrescriptionRequestPreview(
        Request $request,
        Distributor $distributor,
        Order $order,
        MakeCmnPDF $makeCmnPDFAction,
    ): Response {
        $this->authorize('read-document', $order);
        $this->authorize('read-document-request', $distributor);

        $customCmnParams = [
            'branded' => $request->boolean('branded'),
            'product_ids' => $request->get('product_ids'),
        ];

        Validator::validate($customCmnParams, [
            'branded' => 'nullable|boolean',
            'product_ids' => 'nullable|array',
            'product_ids.*' => ['integer', Rule::in($order->products()->pluck('id')->toArray())],
        ]);

        //filter products by product_ids
        $filteredProducts = $order->products()->when(!empty($customCmnParams['product_ids']), function ($query) use ($customCmnParams) {
            return $query->whereIn('products.id', $customCmnParams['product_ids']);
        })->get();

        $pdf = $makeCmnPDFAction->execute(
            distributor: $order->distributor,
            providerUser: $order->providerUser,
            patient: $order->patient,
            products: $filteredProducts,
            provider: $order->provider,
            facility: $order->facility,
            brandedProducts: $customCmnParams['branded'] ?? true,
            icd10Codes: $order->diagnosisCodes,
        )->output();

        return new Response(base64_encode($pdf));
    }

    public function requestAobSignature(
        CreateAobDocumentRequest $request,
        Distributor $distributor,
        Order $order,
        CreateDocumentRequest $createDocumentRequestAction,
        SendDigitalRequest $sendDigitalRequestAction,
        SendAobRequestAction $sendAobRequestAction,
    ): DocumentRequestResource {
        $this->authorize('read-order', [$distributor, $order]);
        $this->authorize('write-document-request', $order);

        $patient = $order->patient;

        abort_if(
            $request->validated('delivered_by_email') && empty($patient->email),
            Response::HTTP_UNPROCESSABLE_ENTITY,
            __('errors.patient.email_not_found'),
        );

        abort_if(
            $request->validated('delivered_by_phone') && empty($patient->getPreferredPhoneNumber()),
            Response::HTTP_UNPROCESSABLE_ENTITY,
            __('errors.patient.email_not_found'),
        );

        $user = getUser();

        $documentRequestData = [
            'order_id' => $order->id,
            'patient_id' => $patient->id,
            'is_digital' => true,
            'type' => DocumentRequestTypeEnum::AOB->value,
        ];

        $documentRequest = $createDocumentRequestAction->execute(
            $distributor,
            $user,
            DocumentRequestRequestTypeEnum::ON_DEMAND,
            $documentRequestData,
        );

        $sendDigitalRequestAction->execute(
            documentRequest: $documentRequest,
            user: $user,
        );

        $sendAobRequestAction->execute(
            aobRequestData: $request->validated(),
            patient: $patient,
            documentRequest: $documentRequest,
            user: $user,
            distributor: $distributor,
            order: $order,
        );

        return DocumentRequestResource::make($documentRequest);
    }

    public function aobPreview(CreateAobDocumentRequest $request, Distributor $distributor, Order $order, PrepareAobFormData $prepareAobFormPreviewData): Response
    {
        $this->authorize('read-order', [$distributor, $order]);
        $aobTemplateName = DistributorAobTemplateNameEnum::from($request->validated('aob_template_name'));

        $pdf = Pdf::loadView(
            'pdf.aob.' . $aobTemplateName->value,
            $prepareAobFormPreviewData->execute($aobTemplateName, order: $order),
        )->output();

        return new Response(base64_encode($pdf));
    }
}
