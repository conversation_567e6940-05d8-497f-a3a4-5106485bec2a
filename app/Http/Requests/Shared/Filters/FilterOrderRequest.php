<?php

namespace App\Http\Requests\Shared\Filters;

use App\Enums\OrderSourceEnum;
use App\Enums\OrderStatusEnum;
use App\Enums\OrderTypeEnum;
use App\Filters\AbstractFilter;
use App\Http\Requests\Shared\PaginationRequest;
use App\Rules\SortValidationRule;
use Illuminate\Validation\Rules\Enum;

class FilterOrderRequest extends PaginationRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return $this->normalizeFilters([
            ...parent::rules(),
            'id' => 'int',
            'global_id' => 'string',
            'status' => 'array',
            'type' => 'array',
            'source' => 'array',
            'status.*' => [new Enum(OrderStatusEnum::class)],
            'type.*' => [new Enum(OrderTypeEnum::class)],
            'source.*' => [new Enum(OrderSourceEnum::class)],
            'patient_id' => 'int|exists:patients,id',
            'provider_id' => 'int|exists:providers,id',
            'created_by' => 'int|exists:users,id',
            'provider_user_id' => 'int|exists:users,id', // assigned provider user
            'distributor_id' => 'int|exists:distributors,id',
            'distributor_user_id' => [ // assigned distributor user
                function ($attribute, $value, $fail) {
                    if (is_numeric($value) || AbstractFilter::NULL_VALUE === $value) {
                        return true;
                    } else {
                        $fail($attribute . ' could be of type integer or null.');
                    }
                },
            ],
            'product_ids' => 'array',
            'product_ids.*' => 'int|exists:products,id',
            'created_from' => 'date_format:Y-m-d',
            'created_to' => 'date_format:Y-m-d|after_or_equal:created_from',
            'updated_from' => 'date_format:Y-m-d',
            'updated_to' => 'date_format:Y-m-d|after_or_equal:updated_from',
            'follow_up_at' => 'date_format:Y-m-d',
            'follow_up_at_from' => 'date_format:Y-m-d',
            'follow_up_at_to' => 'date_format:Y-m-d|after_or_equal:follow_up_at_from',
            'sort_by' => 'in:type,status,created_at,updated_at,follow_up_at',
            'sort' => [
                'array',
                new SortValidationRule(['id', 'type', 'status', 'source', 'created_at', 'updated_at', 'follow_up_at', 'created_by.first_name', 'patient.first_name', 'patient.last_name', 'patient.date_of_birth', 'provider_user.first_name', 'distributor.name', 'cancellation.reason']),
            ],
            'filter.closed_at_from' => 'date_format:Y-m-d',
            'filter.closed_at_to' => 'date_format:Y-m-d|after_or_equal:filter.closed_at_from',
            'external_manufacturer_user_id' => [ // assigned external manufacturer user
                function ($attribute, $value, $fail) {
                    if (is_numeric($value) || AbstractFilter::NULL_VALUE === $value) {
                        return true;
                    } else {
                        $fail($attribute . ' could be of type integer or null.');
                    }
                },
            ],
        ]);
    }
}
