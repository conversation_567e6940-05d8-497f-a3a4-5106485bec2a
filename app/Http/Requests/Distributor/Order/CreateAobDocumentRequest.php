<?php

namespace App\Http\Requests\Distributor\Order;

use App\Enums\OrganizationSettingNameEnum;
use App\Models\Distributor;
use Illuminate\Foundation\Http\FormRequest;

class CreateAobDocumentRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        /** @var Distributor $distributor */
        $distributor = request('distributor');
        $setting = $distributor->organizationSettings()->where('name', OrganizationSettingNameEnum::AOB_TEMPLATE_NAMES)->firstOrFail();

        return [
            'aob_template_name' => 'required|string|in:' . implode(',', $setting->value),
        ];
    }
}
