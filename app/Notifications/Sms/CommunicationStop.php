<?php

namespace App\Notifications\Sms;

use App\Enums\LogGroupEnum;
use App\Enums\MessageConfigurationConditionEnums;
use App\Enums\MessageConfigurationScenarioEnums;
use App\Extensions\Logger;
use App\Helpers\DistributorMessageHelper;
use App\Models\Lead;
use App\Notifications\Channels\SMSChannel;
use App\Notifications\Traits\FromNumber;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Notification;

class CommunicationStop extends Notification implements ShouldQueue
{
    use FromNumber;
    use Queueable;

    /**
     * Create a new notification instance.
     */
    public function __construct(protected ?string $fromNumber = null)
    {
    }

    public function getAuditContext(): array
    {
        return [
            'scenario' => MessageConfigurationScenarioEnums::DEFAULT->value,
            'condition' => MessageConfigurationConditionEnums::OPT_OUT->value,
        ];
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param object $notifiable
     *
     * @return string
     */
    public function via(object $notifiable): string
    {
        return SMSChannel::class;
    }

    /**
     * Get the SMS representation of the notification.
     */
    public function toSMS(object $notifiable): string
    {
        if ($notifiable instanceof Lead) {
            if (!$notifiable->distributorCampaign->opt_out_text) {
                Logger::warning('Opt-out text should be set for leads', LogGroupEnum::DEFAULT, [
                    'campaign_id' => $notifiable->distributorCampaign->id,
                ]);
                throw new Exception('Opt-out text should be set for leads');
            }

            return $notifiable->distributorCampaign->opt_out_text;
        }
        // instance of patient
        $fallback = __('communications.patient.stop', ['organizationName' => $notifiable->organization?->name ?? 'Shuttle Health']);
        $msg = DistributorMessageHelper::getDistributorMessage(
            $notifiable->organization?->id,
            MessageConfigurationScenarioEnums::DEFAULT->value,
            MessageConfigurationConditionEnums::OPT_OUT->value,
            $fallback,
        );

        // Replace placeholders in the message
        return DistributorMessageHelper::replacePlaceholdersInOptInOptOut($notifiable, $msg, $notifiable->organization);
    }
}
