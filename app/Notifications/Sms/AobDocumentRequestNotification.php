<?php

namespace App\Notifications\Sms;

use App\Models\Distributor;
use App\Models\Order;
use App\Models\User;
use App\Notifications\Channels\SMSChannel;
use App\Notifications\Traits\FromNumber;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Str;

class AobDocumentRequestNotification extends Notification
{
    use FromNumber;
    use Queueable;
    public function __construct(
        private ?string $fromNumber = null,
        private readonly User $user,
        private readonly Distributor $distributor,
        private Order $order,
        private readonly string $aobUrl,
    ) {
        $this->order = $order->withoutRelations();
    }

    public function via(object $notifiable): string
    {
        return SMSChannel::class;
    }

    public function toSMS(object $notifiable): string
    {
        return __(
            'communications.order.aob-request',
            [
                'patientName' => $notifiable->getFullName(),
                'distributorName' => $this->order->distributor->name,
                'username' => $this->user->getFullName(),
                'distributorOrgName' => $this->distributor->name,
                'link' => Str::start($this->aobUrl, 'https://'),
            ],
        );
    }
}
