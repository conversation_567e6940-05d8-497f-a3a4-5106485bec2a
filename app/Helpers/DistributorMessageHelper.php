<?php

namespace App\Helpers;

use App\Models\Distributor;
use App\Models\MessageConfiguration;
use App\Models\Order;
use App\Utils\PhoneNumberConverter;

class DistributorMessageHelper
{
    /**
     * Get a distributor message from MessageConfiguration or fallback.
     *
     * @param int $distributorId
     * @param string $scenario
     * @param string $condition
     * @param string|null $fallback
     * @return string|null
     */
    public static function getDistributorMessage(int $distributorId, string $scenario, string $condition, ?string $fallback = null): ?string
    {
        $config = MessageConfiguration::where('organization_type', Distributor::class)
            ->where('organization_id', $distributorId)
            ->where('scenario', $scenario)
            ->where('condition', $condition)
            ->first();

        if ($config) {
            if ($config->active) {
                return !empty($config->content) ? $config->content : $fallback;
            } else {
                return null;
            }
        }

        return $fallback;
    }

    /**
     * Replace placeholders in the message with provided values.
     *
     * @param string $message
     * @param array $values (associative array: placeholder enum name => value)
     * @return string
     */
    public static function replaceLeadPlaceholders($notifiable, string $htmlContent): string
    {
        $distributor = $notifiable->distributorCampaign->distributor;
        // Prepare dynamic params
        $params = [
            '{PATIENT_NAME}' => $notifiable->first_name ?? '',
            '{DISTRIBUTOR_NAME}' => $distributor->name ?? '',
            '{DISTRIBUTOR_WEBSITE}' => $distributor->organizationSettings()->where('name', 'website_url')->get()->value('value') ?? '',
            '{DISTRIBUTOR_PHONE}' => PhoneNumberConverter::formatPhoneNumber($distributor->phone) ?? '',
            '{ORGANIZATION_NAME}' => $distributor->name ?? '',
        ];

        // Replace params in html_content
        return strtr($htmlContent, $params);
    }

    public static function replaceOrderPlaceholders(object $notifiable, Order $order, string $htmlContent): string
    {
        $assignedDistributor = $order->escalation?->assignedOrder?->distributor;
        // Prepare dynamic params
        $params = [
            '{PATIENT_NAME}' => $notifiable->first_name ?? '',
            '{DISTRIBUTOR_NAME}' => $order->distributor->name ?? '',
            '{DISTRIBUTOR_WEBSITE}' => $order->distributor->organizationSettings()->where('name', 'website_url')->get()->value('value') ?? '',
            '{DISTRIBUTOR_PHONE}' => PhoneNumberConverter::formatPhoneNumber($order->distributor->phone) ?? '',
            '{ORGANIZATION_NAME}' => $order->distributor->name ?? '',
            '{ASSIGNED_DISTRIBUTOR_NAME}' => $assignedDistributor?->name,
            '{TRACKING_NUMBER}' => $order->shipping?->tracking_number ?? '',
        ];

        // Replace params in html_content
        return strtr($htmlContent, $params);
    }

    /**
     * Replace placeholders in the message with provided values.
     *
     * @param string $message
     * @param array $values (associative array: placeholder enum name => value)
     * @return string
     */
    public static function replacePlaceholdersInOptInOptOut($notifiable, string $htmlContent, $distributor): string
    {
        // Prepare dynamic params
        $params = [
            '{PATIENT_NAME}' => $notifiable->first_name ?? '',
            '{DISTRIBUTOR_NAME}' => $distributor->name ?? '',
            '{DISTRIBUTOR_WEBSITE}' => $distributor->organizationSettings()->where('name', 'website_url')->get()->value('value') ?? '',
            '{DISTRIBUTOR_PHONE}' => PhoneNumberConverter::formatPhoneNumber($distributor->phone) ?? '',
            '{ORGANIZATION_NAME}' => $distributor->name ?? '',
        ];

        // Replace params in html_content
        return strtr($htmlContent, $params);
    }
}
