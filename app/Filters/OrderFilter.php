<?php

namespace App\Filters;

use App\Filters\Order\ClosedFromFilter;
use App\Filters\Order\ClosedToFilter;
use App\Filters\Order\CreatedByFilter;
use App\Filters\Order\DistributorIDFilter;
use App\Filters\Order\DistributorUserIdFilter;
use App\Filters\Order\ExternalManufacturerUserIdFilter;
use App\Filters\Order\FollowUpAtFilter;
use App\Filters\Order\FollowUpFromFilter;
use App\Filters\Order\FollowUpToFilter;
use App\Filters\Order\GlobalIdFilter;
use App\Filters\Order\PatientIDFilter;
use App\Filters\Order\ProductIDsFilter;
use App\Filters\Order\ProductManufacturerIDsFilter;
use App\Filters\Order\ProviderIdFilter;
use App\Filters\Order\ProviderUserIdFilter;
use App\Filters\Shared\CreatedFromFilter;
use App\Filters\Shared\CreatedToFilter;
use App\Filters\Shared\IdFilter;
use App\Filters\Shared\SourceFilter;
use App\Filters\Shared\StatusFilter;
use App\Filters\Shared\TypeFilter;
use App\Filters\Shared\UpdatedFromFilter;
use App\Filters\Shared\UpdatedToFilter;

class OrderFilter extends AbstractFilter
{
    protected array $filters = [
        'created_by' => CreatedByFilter::class,
        'created_from' => CreatedFromFilter::class,
        'created_to' => CreatedToFilter::class,
        'distributor_id' => DistributorIDFilter::class,
        'distributor_user_id' => DistributorUserIdFilter::class,
        'follow_up_at' => FollowUpAtFilter::class,
        'follow_up_from' => FollowUpFromFilter::class,
        'follow_up_to' => FollowUpToFilter::class,
        'id' => IdFilter::class,
        'global_id' => GlobalIdFilter::class,
        'patient_id' => PatientIDFilter::class,
        'product_ids' => ProductIDsFilter::class,
        'product_manufacturer_ids' => ProductManufacturerIDsFilter::class,
        'provider_id' => ProviderIdFilter::class,
        'provider_user_id' => ProviderUserIdFilter::class,
        'source' => SourceFilter::class,
        'status' => StatusFilter::class,
        'type' => TypeFilter::class,
        'updated_from' => UpdatedFromFilter::class,
        'updated_to' => UpdatedToFilter::class,
        'closed_at_from' => ClosedFromFilter::class,
        'closed_at_to' => ClosedToFilter::class,
        'external_manufacturer_user_id' => ExternalManufacturerUserIdFilter::class,
    ];
}
